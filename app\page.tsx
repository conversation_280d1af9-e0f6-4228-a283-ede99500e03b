"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Shield, Eye, Zap, CheckCircle, AlertTriangle, Star, BarChart3, FileText, ChevronUp } from "lucide-react"
import { useState, useEffect } from "react"

export default function Home() {
  const [showBackToTop, setShowBackToTop] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const handleNavClick = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-white to-slate-100">
      {/* Header */}
      <header className="fixed top-0 w-full bg-white/70 backdrop-blur-md border-b border-white/20 z-50 shadow-sm">
        <div className="flex items-center justify-between px-8 py-4 max-w-7xl mx-auto">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-900 to-slate-700 rounded-lg flex items-center justify-center">
              <Shield className="w-4 h-4 text-white" />
              <Eye className="w-3 h-3 text-white -ml-1" />
            </div>
            <span className="font-bold text-xl text-slate-900">Aurem</span>
          </div>

          <nav className="hidden md:flex items-center gap-2">
            <button
              onClick={() => handleNavClick("#features")}
              className="text-slate-600 hover:text-slate-900 font-medium px-4 py-2 rounded-full hover:bg-white/50 transition-all duration-200"
            >
              Features
            </button>
            <button
              onClick={() => handleNavClick("#pricing")}
              className="text-slate-600 hover:text-slate-900 font-medium px-4 py-2 rounded-full hover:bg-white/50 transition-all duration-200"
            >
              Pricing
            </button>
            <button
              onClick={() => handleNavClick("#faq")}
              className="text-slate-600 hover:text-slate-900 font-medium px-4 py-2 rounded-full hover:bg-white/50 transition-all duration-200"
            >
              FAQ
            </button>
            <button
              onClick={() => handleNavClick("#why-us")}
              className="text-slate-600 hover:text-slate-900 font-medium px-4 py-2 rounded-full hover:bg-white/50 transition-all duration-200"
            >
              Why Us
            </button>
          </nav>

          <Button className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-200">
            Get Started
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <main className="flex flex-col items-center justify-center px-8 py-16 pt-32 max-w-4xl mx-auto text-center">
        <p className="text-sm text-slate-500 uppercase tracking-wider mb-8">
          AI SAFETY • FACT-CHECKING • SLACK INTEGRATION
        </p>

        <div className="mb-6">
          <div className="flex items-center justify-center gap-4 mb-4">
            <h1 className="text-5xl md:text-6xl font-bold text-slate-900 font-canela">Never Trust</h1>
            <div className="bg-red-500 rounded-full p-2">
              <Eye className="w-6 h-6 text-white" />
            </div>
          </div>

          <div className="flex items-center justify-center">
            <span className="text-5xl md:text-6xl font-bold text-slate-900 font-canela">Blind</span>
            <div className="mx-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full px-6 py-2 flex items-center">
              <div className="w-6 h-6 bg-white rounded-full shadow-sm flex items-center justify-center">
                <CheckCircle className="w-4 h-4 text-green-500" />
              </div>
            </div>
            <span className="text-5xl md:text-6xl font-bold text-slate-900 font-canela">ly Again</span>
          </div>
        </div>

        <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-8 font-canela">Sleep Better. Trust AI.</h2>

        <div className="max-w-md mx-auto mb-8">
          <p className="text-slate-600 mb-4 font-inter text-lg">
            Aurem scores every AI answer, flags risks, and adds citations — all inside Slack.
          </p>
        </div>

        <div className="flex flex-col items-center gap-4 mb-12">
          <Button className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-full font-medium font-inter text-lg">
            Add to Slack
          </Button>
          <a href="#demo" className="text-slate-600 hover:text-slate-900 underline font-inter">
            View Demo
          </a>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto border">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-white text-sm font-bold">
              S
            </div>
            <span className="font-medium">Slack</span>
            <span className="text-slate-500 text-sm">#general</span>
          </div>
          <div className="bg-slate-50 rounded p-3 mb-2">
            <p className="text-sm text-slate-700">AI: "The capital of Australia is Sydney."</p>
          </div>
          <div className="bg-red-50 border border-red-200 rounded p-3 flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-red-500" />
            <div className="flex-1">
              <p className="text-sm font-medium text-red-700">Confidence: 15% - High Risk</p>
              <p className="text-xs text-red-600">Uncited claim detected. Actual capital: Canberra</p>
            </div>
          </div>
        </div>
      </main>

      {/* How It Works Section */}
      <section className="bg-slate-50 py-16">
        <div className="max-w-6xl mx-auto px-8">
          <h3 className="text-3xl font-bold text-slate-900 text-center mb-12 font-canela">How It Works</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">AI Generates Response</h4>
              <p className="text-slate-600 font-inter">Your team uses ChatGPT, Bard, or any AI tool in Slack</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Aurem Checks</h4>
              <p className="text-slate-600 font-inter">Real-time fact-checking and confidence scoring</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-slate-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Team Sleeps Better</h4>
              <p className="text-slate-600 font-inter">Trust AI with reports, citations, and risk alerts</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16">
        <div className="max-w-6xl mx-auto px-8">
          <h3 className="text-3xl font-bold text-slate-900 text-center mb-12 font-canela">Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Confidence Scoring</h4>
              <p className="text-sm text-slate-600 font-inter">0-100 accuracy scores for every AI response</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Inline Citations</h4>
              <p className="text-sm text-slate-600 font-inter">
                Automatic source verification and "Uncited Claim" alerts
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="w-6 h-6 text-yellow-600" />
              </div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Weekly Reports</h4>
              <p className="text-sm text-slate-600 font-inter">Comprehensive accuracy reports and audit trails</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-slate-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-slate-600" />
              </div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Slack-First</h4>
              <p className="text-sm text-slate-600 font-inter">Seamless integration, API-ready for enterprise</p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Us Section */}
      <section id="why-us" className="bg-slate-50 py-16">
        <div className="max-w-4xl mx-auto px-8 text-center">
          <h3 className="text-3xl font-bold text-slate-900 mb-12 font-canela">Why Teams Choose Aurem</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex items-start gap-3">
              <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
              <p className="text-slate-700 font-inter">Reduce compliance risk instantly</p>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
              <p className="text-slate-700 font-inter">Build trust in AI rollouts</p>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
              <p className="text-slate-700 font-inter">Seamless Slack integration, zero training needed</p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-16">
        <div className="max-w-6xl mx-auto px-8">
          <h3 className="text-3xl font-bold text-slate-900 text-center mb-12 font-canela">Pricing</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-8 border">
              <h4 className="font-bold text-xl text-slate-900 mb-2 font-canela">Free</h4>
              <p className="text-3xl font-bold text-slate-900 mb-4">
                $0<span className="text-sm text-slate-500">/month</span>
              </p>
              <p className="text-slate-600 mb-6 font-inter"> 50 checks/month</p>
              <Button className="w-full bg-slate-600 hover:bg-slate-700 text-white">Start Free</Button>
            </div>
            <div className="bg-white rounded-lg shadow-lg p-8 border-2 border-green-500 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-1 rounded-full text-sm">
                Popular
              </div>
              <h4 className="font-bold text-xl text-slate-900 mb-2 font-canela">Pro</h4>
              <p className="text-3xl font-bold text-slate-900 mb-4">
                $9<span className="text-sm text-slate-500">/month</span>
              </p>
              <p className="text-slate-600 mb-6 font-inter"> 3000 checks/month</p>
              <Button className="w-full bg-green-600 hover:bg-green-700 text-white">Start Free</Button>
            </div>
            <div className="bg-white rounded-lg shadow-lg p-8 border">
              <h4 className="font-bold text-xl text-slate-900 mb-2 font-canela">Enterprise</h4>
              <p className="text-3xl font-bold text-slate-900 mb-4">
                $199<span className="text-sm text-slate-500">/month</span>
              </p>
              <p className="text-slate-600 mb-6 font-inter">Unlimited + White-label</p>
              <Button className="w-full bg-slate-600 hover:bg-slate-700 text-white">Contact Sales</Button>
            </div>
          </div>
        </div>
      </section>

      {/* What Teams Say Section */}
      <section className="bg-slate-50 py-16">
        <div className="max-w-4xl mx-auto px-8 text-center">
          <h3 className="text-3xl font-bold text-slate-900 mb-12 font-canela">What Teams Say</h3>
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="flex justify-center mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
              ))}
            </div>
            <blockquote className="text-xl text-slate-700 mb-6 font-inter italic">
              "Finally, my compliance team trusts AI outputs. Aurem gives us the confidence we need for enterprise AI
              adoption."
            </blockquote>
            <div className="flex items-center justify-center gap-4">
              <div className="w-12 h-12 bg-slate-300 rounded-full"></div>
              <div>
                <p className="font-semibold text-slate-900">BETA user</p>
                <p className="text-slate-600 text-sm">{""}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-16">
        <div className="max-w-4xl mx-auto px-8">
          <h3 className="text-3xl font-bold text-slate-900 text-center mb-12 font-canela">
            Frequently Asked Questions
          </h3>
          <div className="space-y-8">
            <div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">How does Aurem work?</h4>
              <p className="text-slate-600 font-inter">
                It audits AI answers in real time, flags hallucinations, and adds citations directly in your Slack
                conversations.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Does it store our data?</h4>
              <p className="text-slate-600 font-inter">
                No, it runs checks securely without storing sensitive information. All processing happens in real-time.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">Who should use Aurem?</h4>
              <p className="text-slate-600 font-inter">
                Teams using ChatGPT, Bard, or AI copilots inside Slack who need reliable fact-checking and compliance.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">What AI models does it support?</h4>
              <p className="text-slate-600 font-inter">
                We support all major AI models including GPT-4, Claude, Bard, and custom enterprise AI solutions.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-slate-900 mb-2 font-canela">How accurate is the confidence scoring?</h4>
              <p className="text-slate-600 font-inter">
                Our confidence scores achieve 94% accuracy in identifying potential hallucinations across enterprise
                datasets.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="py-16"></section>

      {/* Footer Section */}
      <footer className="bg-slate-900 text-white py-12">
        <div className="max-w-6xl mx-auto px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <p className="text-slate-400 font-inter">Built for teams who care about trust.</p>
            </div>
            <div>
              <h5 className="font-semibold mb-4">Product</h5>
              <ul className="space-y-2 text-slate-400">
                <li>
                  <a href="#" className="hover:text-white">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Docs
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Slack Marketplace
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h5 className="font-semibold mb-4">Company</h5>
              <ul className="space-y-2 text-slate-400">
                <li>
                  <a href="#" className="hover:text-white">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h5 className="font-semibold mb-4">Support</h5>
              <ul className="space-y-2 text-slate-400">
                <li>
                  <a href="#" className="hover:text-white">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Terms of Service
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Security
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-800 mt-8 pt-8 text-center text-slate-400">
            <p>© 2025 Aurem. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 backdrop-blur-sm"
          aria-label="Back to top"
        >
          <ChevronUp className="w-6 h-6" />
        </button>
      )}
    </div>
  )
}
